<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Storyboard Generator</title>
    <style>
        /* Global Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #2c3e50;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 8px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            color: #7f8c8d;
        }

        /* Tab Navigation */
        .tab-navigation {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .tab-button {
            flex: 1;
            padding: 15px;
            background: transparent;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: #7f8c8d;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-button:hover {
            background: #f8f9fa;
            color: #2c3e50;
        }

        .tab-button.active {
            background: #3498db;
            color: white;
            border-bottom: 3px solid #2980b9;
        }

        /* Tab Content */
        .tab-content {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .tab-content.active {
            display: block;
        }

        .tab-content h2 {
            color: #34495e;
            margin-bottom: 20px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        /* Form Styles */
        .form-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .info-box {
            background: #ebf3fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }

        .info-box h4 {
            margin-bottom: 10px;
            color: #2980b9;
        }

        .info-box ul {
            margin-left: 20px;
        }

        /* PowerPoint Info Display */
        .powerpoint-info {
            text-align: center;
            padding: 40px;
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            border-radius: 15px;
            border: 2px solid #27ae60;
            margin: 20px 0;
        }

        .powerpoint-info .success-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .powerpoint-info h3 {
            color: #27ae60;
            margin-bottom: 15px;
            font-size: 24px;
        }

        .powerpoint-info p {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 16px;
        }

        .powerpoint-info .file-info {
            background: rgba(255, 255, 255, 0.8);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        .powerpoint-info .download-hint {
            font-style: italic;
            color: #7f8c8d;
            font-size: 14px;
        }

        /* Buttons */
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            background: linear-gradient(45deg, #2980b9, #1f618d);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-primary {
            width: 100%;
            justify-content: center;
            font-size: 16px;
            padding: 15px;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        }

        .btn-secondary:hover {
            background: linear-gradient(45deg, #7f8c8d, #6c7b7d);
        }

        /* Two Column Layout */
        .two-columns {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .three-columns {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        .four-columns {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr;
            gap: 15px;
        }

        /* Advanced Options */
        .advanced-options {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .checkbox-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox-item input[type="checkbox"] {
            width: auto;
        }

        /* Color Picker */
        .color-input {
            width: 60px !important;
            height: 40px !important;
            padding: 0 !important;
            border: 2px solid #e1e8ed !important;
            cursor: pointer;
        }

        /* Brand Preview */
        .brand-preview {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        /* Style Grid */
        .style-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .style-card {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #e1e8ed;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .style-card:hover {
            border-color: #3498db;
        }

        .style-card.selected {
            border-color: #3498db;
            background: #ebf3fd;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 600px;
            position: relative;
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e1e8ed;
        }

        .modal-header h3 {
            color: #2c3e50;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #000;
        }

        /* Progress Bar */
        .progress-container {
            margin: 20px 0;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: 600;
            color: #7f8c8d;
        }

        /* Status Messages */
        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .status-message.info {
            background: #ebf3fd;
            border-left: 4px solid #3498db;
            color: #2980b9;
        }

        .status-message.success {
            background: #e8f5e8;
            border-left: 4px solid #27ae60;
            color: #27ae60;
        }

        .status-message.error {
            background: #fdebeb;
            border-left: 4px solid #e74c3c;
            color: #c0392b;
        }

        .status-message.show {
            display: block;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .tab-navigation {
                flex-direction: column;
            }
            
            .form-grid,
            .two-columns,
            .three-columns,
            .four-columns {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }

        /* Loading Spinner */
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
            display: none;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Validation */
        .validation-errors {
            background: #fdebeb;
            border: 1px solid #e74c3c;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }

        .validation-errors h4 {
            color: #c0392b;
            margin-bottom: 10px;
        }

        .validation-errors ul {
            margin-left: 20px;
            color: #c0392b;
        }

        .config-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .config-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }

        .config-section h4 {
            color: #34495e;
            margin-bottom: 10px;
        }

        .config-section ul {
            list-style: none;
            padding: 0;
        }

        .config-section li {
            padding: 3px 0;
            color: #7f8c8d;
        }

        /* Paste Button Styling */
        .paste-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            padding: 4px 8px;
            font-size: 12px;
            background: rgba(52, 152, 219, 0.1);
            border: 1px solid #3498db;
            border-radius: 4px;
            cursor: pointer;
            z-index: 10;
        }

        .paste-btn:hover {
            background: rgba(52, 152, 219, 0.2);
            transform: none;
            box-shadow: none;
        }

        /* Textarea container for paste button positioning */
        .textarea-container {
            position: relative;
        }

        .textarea-container textarea {
            padding-right: 70px; /* Make space for paste button */
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎬 AI Storyboard Generator</h1>
            <p>Professional storyboard generation with advanced customization</p>
        </div>

        <!-- Tab Navigation -->
        <div class="tab-navigation">
            <button class="tab-button active" data-tab="project-setup">📝 Project Setup</button>
            <button class="tab-button" data-tab="brand-customization">🎨 Brand Customization</button>
            <button class="tab-button" data-tab="image-styles">🖼️ Image Styles</button>
            <button class="tab-button" data-tab="advanced-settings">⚙️ Advanced Settings</button>
            <button class="tab-button" data-tab="generate-preview">🚀 Generate & Preview</button>
        </div>

        <!-- Tab 1: Project Setup -->
        <div id="project-setup" class="tab-content active">
            <h2>📝 Project Information</h2>
            
            <div class="form-grid">
                <div>
                    <div class="form-group">
                        <label for="description">Project description</label>
                        <div class="textarea-container">
                            <textarea 
                                id="description" 
                                placeholder="Describe your product, service, or customer experience you want to storyboard. Include key features, target audience, and main value proposition..."
                            ></textarea>
                            <button type="button" class="btn btn-secondary paste-btn" onclick="pasteFromClipboard('description')">
                                📋 Paste
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="expectations">Customer insights & expectations</label>
                        <div class="textarea-container">
                            <textarea 
                                id="expectations"
                                placeholder="Enter each insight on a new line:&#10;• Customers expect fast delivery&#10;• Users prefer mobile-first experience&#10;• Price transparency is crucial&#10;• Support should be easily accessible"
                            ></textarea>
                            <button type="button" class="btn btn-secondary paste-btn" onclick="pasteFromClipboard('expectations')">
                                📋 Paste
                            </button>
                        </div>
                    </div>

                    <!-- <h3>🏢 Industry Context</h3>
                    <div class="two-columns">
                        <div class="form-group">
                            <label for="industry">Select your industry</label>
                            <select id="industry">
                                <option value="General">General</option>
                                <option value="Technology">Technology</option>
                                <option value="Healthcare">Healthcare</option>
                                <option value="Retail">Retail</option>
                                <option value="Finance">Finance</option>
                                <option value="Education">Education</option>
                                <option value="Manufacturing">Manufacturing</option>
                                <option value="Services">Services</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="businessType">Business type</label>
                            <select id="businessType">
                                <option value="B2C">B2C</option>
                                <option value="B2B">B2B</option>
                                <option value="B2B2C">B2B2C</option>
                                <option value="Non-profit">Non-profit</option>
                                <option value="Government">Government</option>
                            </select>
                        </div>
                    </div> -->
                </div>

                <div>
                    <div class="info-box">
                        <h4>💡 Tips for better results:</h4>
                        <ul>
                            <li>Be specific about your product/service</li>
                            <li>Mention your target audience</li>
                            <li>Include key features or benefits</li>
                            <li>Describe the main use case</li>
                            <li>Add any unique selling points</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab 2: Brand Customization -->
        <div id="brand-customization" class="tab-content">
            <h2>🎨 Brand Customization</h2>
            
            <div class="two-columns">
                <div>
                    <h3>Brand Information</h3>
                    <!-- <div class="form-group">
                        <label for="brandName">Brand Name</label>
                        <input type="text" id="brandName" placeholder="Enter your brand name">
                    </div> -->

                    <div class="form-group">
                        <label for="logoUpload">Upload Logo</label>
                        <input type="file" id="logoUpload" accept="image/*">
                        <div id="logoPreview" style="margin-top: 10px;"></div>
                    </div>

                    <div class="form-group">
                        <label for="logoPosition">Logo Position</label>
                        <select id="logoPosition">
                            <option value="top-left">Top Left</option>
                            <option value="top-right" selected>Top Right</option>
                            <option value="bottom-left">Bottom Left</option>
                            <option value="bottom-right">Bottom Right</option>
                        </select>
                    </div>
                </div>

                <div>
                    <h3>Color Palette</h3>
                    <div class="form-group">
                        <label for="paletteType">Palette Type</label>
                        <select id="paletteType">
                            <option value="Custom">Custom</option>
                            <option value="Predefined">Predefined</option>
                            <option value="Generated">Generated from Brand Color</option>
                        </select>
                    </div>

                    <div id="customColors">
                        <div class="three-columns">
                            <div class="form-group">
                                <label for="primaryColor">Primary Color</label>
                                <input type="color" id="primaryColor" value="#3498db" class="color-input">
                            </div>
                            <div class="form-group">
                                <label for="secondaryColor">Secondary Color</label>
                                <input type="color" id="secondaryColor" value="#2ecc71" class="color-input">
                            </div>
                            <div class="form-group">
                                <label for="accentColor">Accent Color</label>
                                <input type="color" id="accentColor" value="#f1c40f" class="color-input">
                            </div>
                        </div>
                    </div>

                    <div id="predefinedPalettes" style="display: none;">
                        <div class="form-group">
                            <label for="paletteSelect">Choose Palette</label>
                            <select id="paletteSelect">
                                <option value="Ocean">Ocean</option>
                                <option value="Sunset">Sunset</option>
                                <option value="Forest">Forest</option>
                                <option value="Purple">Purple</option>
                                <option value="Corporate">Corporate</option>
                                <option value="Warm">Warm</option>
                                <option value="Cool">Cool</option>
                                <option value="Monochrome">Monochrome</option>
                            </select>
                        </div>
                        <div id="palettePreview"></div>
                    </div>

                    <!-- <button type="button" class="btn" onclick="previewBrandStyle()">🔍 Preview Brand Style</button> -->
                    <div id="brandPreview" class="brand-preview" style="display: none;"></div>
                </div>
            </div>
        </div>

        <!-- Tab 3: Image Styles -->
        <div id="image-styles" class="tab-content">
            <h2>🖼️ Image Styles</h2>
            
            <h3>🎨 Choose Visual Style</h3>
            <div class="two-columns">
                <div>
                    <div class="form-group">
                        <label for="baseStyle">Base Style</label>
                        <select id="baseStyle">
                            <option value="SKETCHY_BW_GRAPHIC">Sketchy B&W Graphic</option>
                            <option value="CARTOON">Cartoon</option>
                            <option value="REALISTIC">Realistic</option>
                            <option value="WATERCOLOR">Watercolor</option>
                            <option value="OIL_PAINTING">Oil Painting</option>
                            <option value="DIGITAL_ART">Digital Art</option>
                            <option value="PENCIL_SKETCH">Pencil Sketch</option>
                            <option value="INK_DRAWING">Ink Drawing</option>
                            <option value="PASTEL">Pastel</option>
                            <option value="VECTOR_ART">Vector Art</option>
                            <option value="MINIMALIST">Minimalist</option>
                            <option value="VINTAGE">Vintage</option>
                            <option value="ISOMETRIC">Isometric</option>
                        </select>
                    </div>

                    <div class="info-box">
                        <div id="styleDescription">Style description will appear here</div>
                    </div>

                    <h4>🎭 Style Combinations</h4>
                    <div class="form-group">
                        <label for="styleCombination">Pre-made Combinations</label>
                        <select id="styleCombination">
                            <option value="None">None</option>
                            <option value="Business Professional">Business Professional</option>
                            <option value="Creative & Artistic">Creative & Artistic</option>
                            <option value="Technical & Modern">Technical & Modern</option>
                            <option value="Warm & Approachable">Warm & Approachable</option>
                            <option value="Bold & Dynamic">Bold & Dynamic</option>
                            <option value="Classic & Timeless">Classic & Timeless</option>
                        </select>
                    </div>
                </div>

                <div>
                    <h4>✨ Style Modifiers</h4>
                    <div class="form-group">
                        <label>Mood & Atmosphere</label>
                        <div class="checkbox-grid">
                            <div class="checkbox-item">
                                <input type="checkbox" id="mood-professional" value="Professional" checked>
                                <label for="mood-professional">Professional</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="mood-friendly" value="Friendly">
                                <label for="mood-friendly">Friendly</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="mood-modern" value="Modern" checked>
                                <label for="mood-modern">Modern</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="mood-classic" value="Classic">
                                <label for="mood-classic">Classic</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="mood-energetic" value="Energetic">
                                <label for="mood-energetic">Energetic</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="mood-calm" value="Calm">
                                <label for="mood-calm">Calm</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Technical Aspects</label>
                        <div class="checkbox-grid">
                            <div class="checkbox-item">
                                <input type="checkbox" id="tech-clean-lines" value="Clean Lines" checked>
                                <label for="tech-clean-lines">Clean Lines</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="tech-bright-colors" value="Bright Colors" checked>
                                <label for="tech-bright-colors">Bright Colors</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="tech-high-detail" value="High Detail">
                                <label for="tech-high-detail">High Detail</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="tech-simplified" value="Simplified">
                                <label for="tech-simplified">Simplified</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="tech-textured" value="Textured">
                                <label for="tech-textured">Textured</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="tech-soft-lighting" value="Soft Lighting">
                                <label for="tech-soft-lighting">Soft Lighting</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab 4: Advanced Settings -->
        <div id="advanced-settings" class="tab-content">
            <h2>⚙️ Advanced Settings</h2>
            
            <div class="two-columns">
                <div>
                    <h3>📸 Image Settings</h3>
                    <div class="form-group">
                        <label for="imageModel">Image model</label>
                        <select id="imageModel">
                            <option value="OPENAI_DALLE_3">OpenAI DALL-E 3</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="aspectRatio">Image aspect ratio (rectangular only)</label>
                        <select id="aspectRatio">
                            <option value="WIDESCREEN">Widescreen (16:9)</option>
                            <option value="VERTICAL">Vertical (9:16)</option>
                        </select>
                    </div>

                    <h3>📊 Journey Settings</h3>
                    <div class="form-group">
                        <label for="nbSteps">Number of steps (0 = AI decides)</label>
                        <input type="number" id="nbSteps" min="0" max="20" value="0">
                    </div>
                </div>

                <div>
                    <h3>📝 Content Options</h3>
                    <div class="checkbox-grid">
                        <div class="checkbox-item">
                            <input type="checkbox" id="includeEmotions" checked>
                            <label for="includeEmotions">Include Emotions</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="includeChallenges" checked>
                            <label for="includeChallenges">Include Challenges</label>
                        </div>
                    </div>
                    <br/>
                    <h3>🌍 Language Settings</h3>
                    <div class="form-group">
                        <label for="outputLanguage">Output Language</label>
                        <select id="outputLanguage">
                            <option value="English">English</option>
                            <option value="Spanish">Spanish</option>
                            <option value="French">French</option>
                            <option value="German">German</option>
                            <option value="Italian">Italian</option>
                            <option value="Portuguese">Portuguese</option>
                            <option value="Dutch">Dutch</option>
                            <option value="Russian">Russian</option>
                            <option value="Chinese (Simplified)">Chinese (Simplified)</option>
                            <option value="Japanese">Japanese</option>
                            <option value="Korean">Korean</option>
                            <option value="Arabic">Arabic</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="advanced-options">
                <h3>🚫 Terms to Avoid</h3>
                <p style="color: #7f8c8d; margin-bottom: 15px;">Add words or phrases you want to avoid in your storyboard content.</p>
                <div class="two-columns">
                    <div>
                        <div class="form-group">
                            <label for="newTerm">Add term to avoid</label>
                            <input type="text" id="newTerm" placeholder="e.g., 'complicated', 'expensive', 'slow'">
                            <small style="color: #7f8c8d;">Press Enter or click the button to add</small>
                        </div>
                        <button type="button" class="btn btn-secondary" onclick="addAvoidedTerm()">➕ Add term</button>
                    </div>
                    <div>
                        <div class="form-group">
                            <label>Current avoided terms</label>
                            <div id="avoidedTermsList" style="min-height: 60px; border: 1px solid #e1e8ed; border-radius: 8px; padding: 10px; background: #f8f9fa;">
                                <p style="color: #7f8c8d; font-style: italic;">No terms added yet</p>
                            </div>
                        </div>
                        <button type="button" class="btn btn-secondary" onclick="clearAvoidedTerms()">🗑️ Clear all terms</button>
                    </div>
                </div>
            </div>

            <div class="config-summary">
                <h3>ℹ️ Current Settings Summary</h3>
                <div id="settingsSummary" class="config-grid">
                    <!-- Settings summary will be populated here -->
                </div>
                
                <!-- Debug Section -->
                <!-- <div style="margin-top: 20px; padding: 15px; background: #f1f2f3; border-radius: 8px; border-left: 4px solid #3498db;">
                    <h4 style="color: #2c3e50; margin-bottom: 10px;">🔧 Debug Tools</h4>
                    <button type="button" class="btn btn-secondary" onclick="testApiKeyAccess()" style="margin-right: 10px;">
                        🔑 Test API Key Access
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="refreshApiKeyStatus()">
                        🔄 Refresh API Key Status
                    </button>
                    <div id="debugOutput" style="margin-top: 10px; padding: 10px; background: white; border-radius: 4px; font-family: monospace; font-size: 12px; color: #555; display: none;"></div>
                </div> -->
            </div>
        </div>

        <!-- Tab 5: Generate & Preview -->
        <div id="generate-preview" class="tab-content">
            <h2>🎬 Generate & Preview</h2>
            <p>Ready to create your storyboard? Review your settings and generate!</p>

            <div id="configurationSummary" class="config-summary">
                <h3>📋 Current Configuration Summary</h3>
                <br/>
                <div class="config-grid">
                    <div class="config-section">
                        <h4>📝 Project Settings:</h4>
                        <ul id="projectSettingsList"></ul>
                    </div>
                    <div class="config-section">
                        <h4>🎨 Visual Settings:</h4>
                        <ul id="visualSettingsList"></ul>
                    </div>
                    <div class="config-section">
                        <h4>⚙️ Advanced Settings:</h4>
                        <ul id="advancedSettingsList"></ul>
                    </div>
                </div>
            </div>

            <div id="validationErrors" class="validation-errors" style="display: none;">
                <h4>❌ Please fix the following issues before generating:</h4>
                <ul id="errorList"></ul>
            </div>

            <div class="four-columns">
                <div>
                    <button type="button" id="generateStoryboard" class="btn btn-primary">
                        🎬 Generate Storyboard
                    </button>
                </div>
                <div>
                    <button type="button" id="previewStory" class="btn btn-secondary">
                        👁️ Preview Story
                    </button>
                </div>
                <div>
                    <button type="button" id="resetAll" class="btn btn-secondary">
                        🔄 Reset All
                    </button>
                </div>
                <div>
                    <div style="text-align: center; padding: 12px;">
                        <strong id="statusIndicator">📊 Ready</strong>
                    </div>
                </div>
            </div>

            <!-- Progress Section -->
            <div id="progressContainer" class="progress-container">
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill"></div>
                </div>
                <div id="progressText" class="progress-text">Preparing your storyboard...</div>
            </div>

            <div id="loadingSpinner" class="loading-spinner"></div>

            <!-- Status Messages -->
            <div id="statusMessage" class="status-message">
                <span id="statusText"></span>
            </div>

            <!-- Story Preview Section -->
            <div id="storyPreviewSection" style="display: none;">
                <h3>👁️ Story Preview & Editor</h3>
                <div id="storyPreviewContent"></div>
            </div>
            <br/>
            <!-- Download Section -->
            <div id="downloadSection" style="display: none;">
                <h3>📥 Download Results</h3>
                <div class="two-columns">
                    <div>
                        <button type="button" id="downloadBtn" class="download-btn btn btn-primary" style="display: none;">
                            📥 Download PowerPoint Storyboard
                        </button>
                        <div class="file-info" id="generationStats"></div>
                    </div>
                    <div>
                        <div class="info-box">
                            <h4>📋 What's included:</h4>
                            <ul>
                                <li>✅ Complete customer journey</li>
                                <li>✅ AI-generated persona</li>
                                <li>✅ Professional PowerPoint layout</li>
                                <li>✅ DALL-E 3 generated visuals for each step</li>
                                <li>✅ Detailed step descriptions</li>
                                <li>✅ Customizable PowerPoint content</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Generation Progress -->
    <div id="generationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🎬 Generating Storyboard</h3>
            </div>
            <div class="modal-body">
                <div class="progress-container" style="display: block;">
                    <div class="progress-bar">
                        <div id="modalProgressFill" class="progress-fill"></div>
                    </div>
                    <div id="modalProgressText" class="progress-text">Starting generation...</div>
                </div>
                <div class="loading-spinner" id="modalSpinner" style="display: block; margin: 20px auto;"></div>
                <p style="text-align: center; color: #7f8c8d; margin-top: 20px;">
                    Please wait while we create your storyboard. This may take a few minutes.
                </p>
            </div>
        </div>
    </div>

    <script src="storyboard-app.js"></script>
</body>
</html>
