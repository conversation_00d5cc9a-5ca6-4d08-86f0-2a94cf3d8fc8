// Storyboard Generator App - Enhanced Version
class StoryboardApp {
    constructor() {
        this.apiKey = null;
        this.currentStory = null;
        this.brandConfig = {
            brand_name: '',
            logo_data: null,
            logo_position: 'top-right',
            logo_size: [100, 50],
            primary_color: [52, 152, 219],
            secondary_color: [46, 204, 113],
            accent_color: [241, 196, 15],
            background_color: [248, 249, 250],
            text_color: [44, 62, 80],
            primary_font: 'Arial',
            secondary_font: 'Arial'
        };
        this.customStyles = [];
        this.avoidedTerms = [];
        this.storyPreview = null;
        this.init();
    }

    async init() {
        console.log('🚀 StoryboardApp initializing...');
        this.setupEventListeners();
        await this.loadApiKey();
        this.setupTabNavigation();
        this.updateSettingsSummary();
        this.setupFormValidation();
        this.setupModalHandlers();
        console.log('✅ StoryboardApp initialization complete');

        // Test if electronAPI is available
        if (typeof window.electronAPI !== 'undefined') {
            console.log('✅ Electron API is available');
        } else {
            console.log('❌ Electron API is NOT available - running in browser mode');
        }
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Form change handlers
        const formElements = document.querySelectorAll('input, select, textarea');
        formElements.forEach(element => {
            element.addEventListener('change', () => {
                this.updateSettingsSummary();
                this.validateForm();
            });
        });

        // Image style handlers
        document.getElementById('baseStyle').addEventListener('change', (e) => {
            this.updateStyleDescription(e.target.value);
        });

        document.getElementById('paletteType').addEventListener('change', (e) => {
            this.handlePaletteTypeChange(e.target.value);
        });

        // Logo upload handler
        document.getElementById('logoUpload').addEventListener('change', (e) => {
            this.handleLogoUpload(e);
        });

        // Logo position handler
        document.getElementById('logoPosition').addEventListener('change', (e) => {
            this.brandConfig.logo_position = e.target.value;
            console.log('🎨 Logo position changed to:', e.target.value);
            this.updateSettingsSummary();
        });

        // Color change handlers
        document.getElementById('primaryColor').addEventListener('change', (e) => {
            const rgb = this.hexToRgb(e.target.value);
            this.brandConfig.primary_color = [rgb.r, rgb.g, rgb.b];
            console.log('🎨 Primary color changed to:', e.target.value, '→', this.brandConfig.primary_color);
            this.updateSettingsSummary();
        });

        document.getElementById('secondaryColor').addEventListener('change', (e) => {
            const rgb = this.hexToRgb(e.target.value);
            this.brandConfig.secondary_color = [rgb.r, rgb.g, rgb.b];
            console.log('🎨 Secondary color changed to:', e.target.value, '→', this.brandConfig.secondary_color);
            this.updateSettingsSummary();
        });

        document.getElementById('accentColor').addEventListener('change', (e) => {
            const rgb = this.hexToRgb(e.target.value);
            this.brandConfig.accent_color = [rgb.r, rgb.g, rgb.b];
            console.log('🎨 Accent color changed to:', e.target.value, '→', this.brandConfig.accent_color);
            this.updateSettingsSummary();
        });

        // Generation buttons
        document.getElementById('generateStoryboard').addEventListener('click', () => {
            console.log('🎬 Generate Storyboard button clicked - event listener triggered');
            this.generateStoryboard();
        });

        document.getElementById('previewStory').addEventListener('click', () => {
            this.generateStoryPreview();
        });

        document.getElementById('resetAll').addEventListener('click', () => {
            this.resetAll();
        });

        // Download button - will be handled dynamically when file is ready

        // Enhanced paste functionality for text areas
        this.setupPasteEnhancements();

        // Add Enter key support for avoided terms
        const newTermInput = document.getElementById('newTerm');
        if (newTermInput) {
            newTermInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.addAvoidedTerm();
                }
            });
        }
    }

    setupPasteEnhancements() {
        // Add paste enhancements to project description and expectations fields
        const descriptionField = document.getElementById('description');
        const expectationsField = document.getElementById('expectations');

        if (descriptionField) {
            this.enhanceTextAreaPaste(descriptionField, 'Project Description');
        }

        if (expectationsField) {
            this.enhanceTextAreaPaste(expectationsField, 'Customer Insights');
        }
    }

    enhanceTextAreaPaste(element, fieldName) {
        // Add paste event listener for better user feedback
        element.addEventListener('paste', (e) => {
            // Show temporary status message
            this.showStatus(`📋 Pasted content into ${fieldName}`, 'success');
            
            // Auto-resize if needed
            setTimeout(() => {
                this.autoResizeTextArea(element);
                this.updateSettingsSummary();
            }, 100);
        });

        // Add focus styling
        element.addEventListener('focus', () => {
            element.style.borderColor = '#3498db';
            element.style.boxShadow = '0 0 0 3px rgba(52, 152, 219, 0.1)';
        });

        element.addEventListener('blur', () => {
            element.style.borderColor = '#e1e8ed';
            element.style.boxShadow = 'none';
        });

        // Add keyboard shortcuts info
        element.title = `${fieldName} - Use Ctrl+V (Cmd+V on Mac) to paste content`;
    }

    autoResizeTextArea(element) {
        // Auto-resize textarea based on content
        element.style.height = 'auto';
        element.style.height = Math.max(120, element.scrollHeight) + 'px';
    }

    setupModalHandlers() {
        // Modal close handler
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('generationModal');
            if (e.target === modal) {
                // Don't allow closing during generation
            }
        });
    }

    setupTabNavigation() {
        // Initialize first tab
        this.switchTab('project-setup');
    }

    switchTab(tabId) {
        // Update tab buttons
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabId).classList.add('active');

        // Update settings summary when switching to generate tab
        if (tabId === 'generate-preview') {
            this.updateConfigurationSummary();
            this.validateForm();
        }
    }

    async loadApiKey() {
        try {
            this.apiKey = await window.electronAPI.getApiKey();
            console.log('Initial API key load:', this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'null/empty');
            if (!this.apiKey) {
                this.showStatus('Please configure your OpenAI API key in Settings', 'info');
            }
        } catch (error) {
            console.error('Error loading API key:', error);
        }
    }

    async refreshApiKey() {
        try {
            this.apiKey = await window.electronAPI.getApiKey();
            console.log('API key refreshed:', this.apiKey ? `${this.apiKey.substring(0, 10)}...` : 'null/empty');
            return this.apiKey;
        } catch (error) {
            console.error('Error refreshing API key:', error);
            return null;
        }
    }

    setupFormValidation() {
        // Real-time validation
        const requiredFields = ['description'];
        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('input', () => {
                    this.validateForm();
                });
            }
        });
    }

    validateForm() {
        const errors = [];

        const description = document.getElementById('description').value.trim();
        const nbSteps = parseInt(document.getElementById('nbSteps').value);
        const baseStyle = document.getElementById('baseStyle').value;

        console.log('🔍 Form validation - Description:', description);
        console.log('🔍 Form validation - Steps:', nbSteps);
        console.log('🔍 Form validation - Style:', baseStyle);

        if (!description) {
            errors.push('Project description is required');
        }

        if (nbSteps && nbSteps < 3) {
            errors.push('Minimum 3 steps required for a meaningful journey (or set to 0 for AI to decide)');
        }

        if (baseStyle === 'CUSTOM' && !document.getElementById('customStyleDescription')) {
            errors.push('Custom style description is required when using custom style');
        }

        console.log('🔍 Form validation errors:', errors);

        // Update validation display
        const errorContainer = document.getElementById('validationErrors');
        const errorList = document.getElementById('errorList');

        if (errors.length > 0) {
            errorList.innerHTML = errors.map(error => `<li>${error}</li>`).join('');
            errorContainer.style.display = 'block';
            document.getElementById('generateStoryboard').disabled = true;
            document.getElementById('previewStory').disabled = true;
            document.getElementById('statusIndicator').textContent = '❌ Issues';
            console.log('🔍 Form validation FAILED - buttons disabled');
        } else {
            errorContainer.style.display = 'none';
            document.getElementById('generateStoryboard').disabled = false;
            document.getElementById('previewStory').disabled = false;
            document.getElementById('statusIndicator').textContent = '✅ Ready';
            console.log('🔍 Form validation PASSED - buttons enabled');
        }

        return errors.length === 0;
    }

    updateStyleDescription(styleValue) {
        const descriptions = {
            'SKETCHY_BW_GRAPHIC': 'Sketchy graphic style, quick, rough and imprecise marker strokes, not too detailed, with few strokes, white background, black color for lines and a light gray scale for shades.',
            'CARTOON': 'Cartoon-like images with vibrant colors, clean lines, friendly and approachable.',
            'REALISTIC': 'Photo-realistic pictures with high detail, natural lighting, professional photography quality.',
            'WATERCOLOR': 'Watercolor painting style with soft, flowing colors and organic textures.',
            'OIL_PAINTING': 'Oil painting style with rich textures, visible brushstrokes, and classical composition.',
            'DIGITAL_ART': 'Modern digital art with clean lines, vibrant colors, and contemporary aesthetics.',
            'PENCIL_SKETCH': 'Detailed pencil sketch with fine lines, shading, and artistic composition.',
            'INK_DRAWING': 'Bold ink drawing with strong contrasts, clean lines, and minimal color.',
            'PASTEL': 'Soft pastel artwork with gentle colors, smooth transitions, and dreamy atmosphere.',
            'VECTOR_ART': 'Clean vector illustration with geometric shapes, flat colors, and modern design.',
            'MINIMALIST': 'Minimalist style with simple shapes, limited colors, and clean composition.',
            'VINTAGE': 'Vintage illustration style with retro colors, classic typography, and nostalgic feel.',
            'ISOMETRIC': 'Isometric 3D style with geometric precision and modern technical aesthetic.'
        };

        const descElement = document.getElementById('styleDescription');
        descElement.textContent = descriptions[styleValue] || 'Style description not available.';
    }

    handlePaletteTypeChange(paletteType) {
        const customColors = document.getElementById('customColors');
        const predefinedPalettes = document.getElementById('predefinedPalettes');

        if (paletteType === 'Custom') {
            customColors.style.display = 'block';
            predefinedPalettes.style.display = 'none';
        } else if (paletteType === 'Predefined') {
            customColors.style.display = 'none';
            predefinedPalettes.style.display = 'block';
            this.loadPredefinedPalettes();
        } else {
            customColors.style.display = 'block';
            predefinedPalettes.style.display = 'none';
        }
    }

    loadPredefinedPalettes() {
        const palettes = {
            "Ocean": [[52, 152, 219], [46, 204, 113], [26, 188, 156]],
            "Sunset": [[231, 76, 60], [230, 126, 34], [241, 196, 15]],
            "Forest": [[39, 174, 96], [46, 204, 113], [26, 188, 156]],
            "Purple": [[155, 89, 182], [142, 68, 173], [187, 143, 206]],
            "Corporate": [[52, 73, 94], [44, 62, 80], [52, 152, 219]],
            "Warm": [[231, 76, 60], [192, 57, 43], [230, 126, 34]],
            "Cool": [[52, 152, 219], [41, 128, 185], [46, 204, 113]],
            "Monochrome": [[44, 62, 80], [52, 73, 94], [99, 110, 114]]
        };

        document.getElementById('paletteSelect').addEventListener('change', (e) => {
            const selectedPalette = palettes[e.target.value];
            if (selectedPalette) {
                this.brandConfig.primary_color = selectedPalette[0];
                this.brandConfig.secondary_color = selectedPalette[1];
                this.brandConfig.accent_color = selectedPalette[2];
                this.showPalettePreview(selectedPalette);
            }
        });
    }

    showPalettePreview(colors) {
        const preview = document.getElementById('palettePreview');
        preview.innerHTML = '';
        
        const container = document.createElement('div');
        container.style.display = 'flex';
        container.style.gap = '10px';
        container.style.marginTop = '10px';

        colors.forEach(color => {
            const swatch = document.createElement('div');
            swatch.style.width = '40px';
            swatch.style.height = '40px';
            swatch.style.backgroundColor = `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
            swatch.style.borderRadius = '5px';
            swatch.style.border = '2px solid #e1e8ed';
            container.appendChild(swatch);
        });

        preview.appendChild(container);
    }

    handleLogoUpload(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const logoData = e.target.result;
                this.brandConfig.logo_data = logoData.split(',')[1]; // Remove data:image/...;base64, prefix
                
                // Show preview
                const preview = document.getElementById('logoPreview');
                preview.innerHTML = `<img src="${logoData}" alt="Logo Preview" style="max-width: 150px; max-height: 100px; border-radius: 5px;">`;
            };
            reader.readAsDataURL(file);
        }
    }

    previewBrandStyle() {
        // Simple brand preview implementation
        const preview = document.getElementById('brandPreview');
        const brandName = document.getElementById('brandName').value || 'Brand Name';
        
        preview.innerHTML = `
            <div style="padding: 20px; background: white; border-radius: 10px; text-align: center;">
                <h4 style="color: rgb(${this.brandConfig.primary_color.join(',')});">${brandName}</h4>
                <div style="display: flex; justify-content: center; gap: 10px; margin-top: 10px;">
                    <div style="width: 30px; height: 30px; background: rgb(${this.brandConfig.primary_color.join(',')}); border-radius: 5px;"></div>
                    <div style="width: 30px; height: 30px; background: rgb(${this.brandConfig.secondary_color.join(',')}); border-radius: 5px;"></div>
                    <div style="width: 30px; height: 30px; background: rgb(${this.brandConfig.accent_color.join(',')}); border-radius: 5px;"></div>
                </div>
                <p style="margin-top: 10px; color: rgb(${this.brandConfig.text_color.join(',')});">Sample storyboard content</p>
            </div>
        `;
        preview.style.display = 'block';
    }

    addAvoidedTerm() {
        const newTerm = document.getElementById('newTerm').value.trim();
        if (!newTerm) {
            this.showStatus('Please enter a term to avoid', 'error');
            return;
        }
        
        if (this.avoidedTerms.includes(newTerm.toLowerCase())) {
            this.showStatus('Term already added', 'error');
            return;
        }
        
        this.avoidedTerms.push(newTerm.toLowerCase());
        this.updateAvoidedTermsList();
        document.getElementById('newTerm').value = '';
        this.showStatus(`Added "${newTerm}" to avoided terms`, 'success');
        this.updateSettingsSummary();
    }

    updateAvoidedTermsList() {
        const list = document.getElementById('avoidedTermsList');
        if (this.avoidedTerms.length === 0) {
            list.innerHTML = '<p style="color: #7f8c8d; font-style: italic;">No terms added yet</p>';
        } else {
            const terms = this.avoidedTerms.map(term => 
                `<span style="display: inline-block; background: #e1e8ed; padding: 5px 10px; margin: 2px; border-radius: 15px; font-size: 12px;">
                    ${term} <button onclick="window.storyboardApp.removeAvoidedTerm('${term}')" style="background: none; border: none; color: #e74c3c; cursor: pointer; margin-left: 5px;">×</button>
                </span>`
            ).join('');
            list.innerHTML = terms;
        }
    }

    removeAvoidedTerm(term) {
        this.avoidedTerms = this.avoidedTerms.filter(t => t !== term);
        this.updateAvoidedTermsList();
    }

    clearAvoidedTerms() {
        if (this.avoidedTerms.length === 0) {
            this.showStatus('No terms to clear', 'info');
            return;
        }
        
        this.avoidedTerms = [];
        this.updateAvoidedTermsList();
        this.updateSettingsSummary();
        this.showStatus('All avoided terms cleared', 'success');
    }

    saveStyleTemplate() {
        const name = document.getElementById('templateName').value.trim();
        const description = document.getElementById('templateDescription').value.trim();
        
        if (!name || !description) {
            this.showStatus('Please provide both name and description for the template', 'error');
            return;
        }

        const template = {
            name: name,
            description: description,
            base_style: document.getElementById('baseStyle').value,
            mood_keywords: this.getSelectedMoodKeywords(),
            style_modifiers: this.getSelectedTechnicalModifiers(),
            created_by: 'User'
        };

        this.customStyles.push(template);
        this.updateSavedTemplates();
        this.showStatus(`Style template '${name}' saved successfully!`, 'success');
        
        // Clear form
        document.getElementById('templateName').value = '';
        document.getElementById('templateDescription').value = '';
    }

    getSelectedMoodKeywords() {
        const checkboxes = document.querySelectorAll('input[id^="mood-"]:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }

    getSelectedTechnicalModifiers() {
        const checkboxes = document.querySelectorAll('input[id^="tech-"]:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }

    updateSavedTemplates() {
        const container = document.getElementById('savedTemplates');
        const deleteSelect = document.getElementById('templateToDelete');
        
        if (this.customStyles.length === 0) {
            container.innerHTML = '<p style="color: #7f8c8d; font-style: italic;">No saved templates yet</p>';
            deleteSelect.innerHTML = '<option value="">Select template to delete</option>';
        } else {
            const templates = this.customStyles.map((template, index) => 
                `<button onclick="app.loadStyleTemplate(${index})" style="display: block; width: 100%; margin-bottom: 5px; padding: 8px; background: #f8f9fa; border: 1px solid #e1e8ed; border-radius: 5px; cursor: pointer;">
                    📋 ${template.name}
                </button>`
            ).join('');
            container.innerHTML = templates;

            // Update delete dropdown
            deleteSelect.innerHTML = '<option value="">Select template to delete</option>' +
                this.customStyles.map((template, index) => `<option value="${index}">${template.name}</option>`).join('');
        }
    }

    loadStyleTemplate(index) {
        const template = this.customStyles[index];
        if (template) {
            document.getElementById('baseStyle').value = template.base_style;
            this.updateStyleDescription(template.base_style);
            this.showStatus(`Loaded template: ${template.name}`, 'success');
        }
    }

    deleteStyleTemplate() {
        const index = document.getElementById('templateToDelete').value;
        if (index !== '') {
            const template = this.customStyles[parseInt(index)];
            this.customStyles.splice(parseInt(index), 1);
            this.updateSavedTemplates();
            this.showStatus(`Deleted template: ${template.name}`, 'success');
        }
    }

    updateSettingsSummary() {
        const summary = document.getElementById('settingsSummary');
        if (!summary) return;

        const description = document.getElementById('description').value;
        const nbSteps = document.getElementById('nbSteps').value || 'AI-determined';
        const baseStyle = document.getElementById('baseStyle').value;
        const aspectRatio = document.getElementById('aspectRatio').value;
        const language = document.getElementById('outputLanguage').value;
        const includeEmotions = document.getElementById('includeEmotions').checked;
        const includeChallenges = document.getElementById('includeChallenges').checked;

        summary.innerHTML = `
            <div class="config-section">
                <h4>📝 Project:</h4>
                <ul>
                    <li>Description: ${description.substring(0, 50)}${description.length > 50 ? '...' : ''}</li>
                    <li>Steps: ${nbSteps}</li>
                    <li>Language: ${language}</li>
                </ul>
            </div>
            <div class="config-section">
                <h4>🎨 Visual:</h4>
                <ul>
                    <li>Style: ${baseStyle.replace(/_/g, ' ')}</li>
                    <li>Format: ${aspectRatio}</li>
                    <li>Brand: ${this.brandConfig.brand_name || 'Default'}</li>
                </ul>
            </div>
            <div class="config-section">
                <h4>⚙️ Advanced:</h4>
                <ul>
                    <li>Emotions: ${includeEmotions ? '✅' : '❌'}</li>
                    <li>Challenges: ${includeChallenges ? '✅' : '❌'}</li>
                    <li>Avoided Terms: ${this.avoidedTerms.length}</li>
                </ul>
            </div>
        `;
    }

    updateConfigurationSummary() {
        const projectList = document.getElementById('projectSettingsList');
        const visualList = document.getElementById('visualSettingsList');
        const advancedList = document.getElementById('advancedSettingsList');

        const description = document.getElementById('description').value;
        const nbSteps = document.getElementById('nbSteps').value || 'AI-determined';
        const language = document.getElementById('outputLanguage').value;

        projectList.innerHTML = `
            <li>Description: ${description.substring(0, 50)}${description.length > 50 ? '...' : ''}</li>
            <li>Steps: ${nbSteps}</li>
            <li>Language: ${language}</li>
        `;

        const baseStyle = document.getElementById('baseStyle').value;
        const aspectRatio = document.getElementById('aspectRatio').value;

        visualList.innerHTML = `
            <li>Style: ${baseStyle.replace(/_/g, ' ')}</li>
            <li>Format: ${aspectRatio}</li>
            <li>Brand: ${this.brandConfig.brand_name || 'Default'}</li>
        `;

        const includeEmotions = document.getElementById('includeEmotions').checked;
        const includeChallenges = document.getElementById('includeChallenges').checked;
        const imageModel = document.getElementById('imageModel').value;

        advancedList.innerHTML = `
            <li>Emotions: ${includeEmotions ? '✅' : '❌'}</li>
            <li>Challenges: ${includeChallenges ? '✅' : '❌'}</li>
            <li>Image Model: ${imageModel}</li>
            <li>Brand: ${this.brandConfig.brand_name || 'Default'}</li>
        `;
    }

    collectFormData() {
        // Map HTML style values to generator expected values
        const styleMap = {
            'SKETCHY_BW_GRAPHIC': 'sketchy',
            'CARTOON': 'cartoon',
            'REALISTIC': 'realistic',
            'WATERCOLOR': 'custom',
            'OIL_PAINTING': 'custom',
            'DIGITAL_ART': 'custom',
            'PENCIL_SKETCH': 'custom',
            'INK_DRAWING': 'custom',
            'PASTEL': 'custom',
            'VECTOR_ART': 'custom',
            'MINIMALIST': 'custom',
            'VINTAGE': 'custom',
            'ISOMETRIC': 'custom'
        };

        const baseStyleValue = document.getElementById('baseStyle').value;
        const mappedStyle = styleMap[baseStyleValue] || 'realistic';

        // Get custom style description from the style description div if it's a custom style
        let customStyleDescription = '';
        if (mappedStyle === 'custom') {
            const styleDesc = document.getElementById('styleDescription').textContent;
            customStyleDescription = styleDesc !== 'Style description will appear here' ? styleDesc : baseStyleValue.replace(/_/g, ' ').toLowerCase() + ' style';
        }

        // Ensure rectangular aspect ratio (no square images)
        let aspectRatio = document.getElementById('aspectRatio').value;
        if (aspectRatio === 'SQUARE') {
            aspectRatio = 'WIDESCREEN'; // Force rectangular
            document.getElementById('aspectRatio').value = 'WIDESCREEN';
            this.showStatus('⚠️ Square images changed to widescreen for rectangular requirement', 'info');
        }

        // Update brand config with current form values
        this.brandConfig.logo_position = document.getElementById('logoPosition').value;

        return {
            description: document.getElementById('description').value.trim(),
            expectations: document.getElementById('expectations').value.trim(),
            industry: document.getElementById('industry')?.value || 'General',
            businessType: document.getElementById('businessType')?.value || 'B2C',
            imageModel: document.getElementById('imageModel').value,
            aspectRatio: aspectRatio,
            baseStyle: mappedStyle,
            customStyleDescription: customStyleDescription,
            nbSteps: parseInt(document.getElementById('nbSteps').value) || null,
            includeEmotions: document.getElementById('includeEmotions').checked,
            includeChallenges: document.getElementById('includeChallenges').checked,
            outputLanguage: document.getElementById('outputLanguage').value,
            avoidedTerms: this.avoidedTerms,
            brandConfig: this.brandConfig,
            customStyles: this.customStyles,
            moodKeywords: this.getSelectedMoodKeywords(),
            technicalModifiers: this.getSelectedTechnicalModifiers()
        };
    }

    async generateStoryboard() {
        console.log('🎬 Generate Storyboard button clicked!');

        // Always fetch the latest API key
        const apiKey = await this.refreshApiKey();

        if (!apiKey || apiKey.trim() === '') {
            console.log('API key validation failed:', apiKey);
            this.showStatus('❌ OpenAI API key not found. Please go to the Settings menu (⚙️) to configure your API key.', 'error');
            return;
        }

        console.log('✅ API key found, proceeding with generation');

        if (!this.validateForm()) {
            this.showStatus('❌ Please fix the validation errors before generating', 'error');
            return;
        }

        const formData = this.collectFormData();
        console.log('Collected form data:', formData);

        try {
            // Show modal
            this.showGenerationModal();
            
            // Set up progress listener
            window.electronAPI.onStoryboardProgress((event, progressData) => {
                console.log('Progress update:', progressData);
                this.updateModalProgress(null, progressData.message);
            });
            
            // Call Python script to generate actual PowerPoint
            this.updateModalProgress(10, 'Starting storyboard generation...');
            const result = await window.electronAPI.generateStoryboard(formData);
            
            if (result.success) {
                this.updateModalProgress(100, 'Storyboard generation complete!');
                await this.delay(1000);
                
                this.hideGenerationModal();
                this.showStatus('✅ Storyboard generated successfully!', 'success');
                
                // Show download section with actual file
                this.showDownloadSection(result);
                
            } else {
                throw new Error(result.error || 'Unknown error occurred');
            }
            
        } catch (error) {
            console.error('Generation error:', error);
            this.hideGenerationModal();
            this.showStatus(`❌ Generation failed: ${error.message}`, 'error');
        }
    }

    showDownloadSection(result) {
        console.log('Showing download section with result:', result);

        // Store the result for download
        this.currentPowerPointData = result;

        const downloadSection = document.getElementById('downloadSection');
        if (downloadSection) {
            downloadSection.style.display = 'block';

            // Update download button with actual file
            const downloadBtn = downloadSection.querySelector('.download-btn');
            if (downloadBtn) {
                // Set up the event listener (this will replace any existing onclick)
                downloadBtn.onclick = () => this.downloadGeneratedFile(result);
                downloadBtn.innerHTML = `📥 Download ${result.filename}`;
                downloadBtn.style.display = 'inline-flex'; // Make sure it's visible
            }

            // Show file info
            const fileInfo = downloadSection.querySelector('#generationStats');
            if (fileInfo) {
                fileInfo.innerHTML = `
                    <div class="generated-file-info">
                        <h4>📋 Generated PowerPoint Storyboard</h4>
                        <p><strong>File:</strong> ${result.filename}</p>
                        <p><strong>Format:</strong> PowerPoint (.pptx)</p>
                        <p><strong>Generated:</strong> ${new Date(result.timestamp).toLocaleString()}</p>
                        <p><strong>Status:</strong> Ready for download</p>
                    </div>
                `;
            }
        }
    }

    async downloadGeneratedFile(result) {
        try {
            console.log('Downloading generated file:', result);

            // Check if we have file data (new method) or file path (old method)
            if (result.fileData) {
                // New method: Use the PowerPoint download handler with file data
                const downloadResult = await window.electronAPI.downloadPowerPoint(
                    result.fileData,
                    result.filename
                );

                if (downloadResult.success && !downloadResult.canceled) {
                    this.showStatus(`✅ PowerPoint saved to: ${downloadResult.filename}`, 'success');
                } else if (downloadResult.canceled) {
                    this.showStatus('Download canceled', 'info');
                } else {
                    throw new Error(downloadResult.error || 'Download failed');
                }
            } else if (result.file_path) {
                // Old method: Copy file from path (fallback)
                const saveResult = await window.electronAPI.saveFileDialog({
                    title: 'Save Storyboard',
                    defaultPath: result.filename,
                    filters: [
                        { name: 'PowerPoint Presentations', extensions: ['pptx'] },
                        { name: 'All Files', extensions: ['*'] }
                    ]
                });

                if (!saveResult.canceled) {
                    const copyResult = await window.electronAPI.copyFile(result.file_path, saveResult.filePath);

                    if (copyResult.success) {
                        this.showStatus('✅ File saved successfully!', 'success');
                    } else {
                        throw new Error(copyResult.error || 'Failed to copy file');
                    }
                }
            } else {
                throw new Error('No file data or file path available for download');
            }
        } catch (error) {
            console.error('Download error:', error);
            this.showStatus(`❌ Failed to save file: ${error.message}`, 'error');
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async generateStoryPreview() {
        try {
            const projectData = this.gatherProjectData();
            const brandData = this.gatherBrandData();
            const imageData = this.gatherImageData();
            const advancedData = this.gatherAdvancedData();

            // Show generation modal
            this.showGenerationModal();

            // Step 1: Generate story outline
            this.updateModalProgress(20, 'Creating story outline...');
            
            const storyPrompt = `Create a customer journey story for ${projectData.projectName}.
                Brand: ${brandData.companyName}
                Target Persona: ${projectData.targetPersona}
                Product/Service: ${projectData.productService}
                Story Type: ${advancedData.storyType}
                Tone: ${advancedData.tone}
                
                Create ${advancedData.sceneCount} scenes showing the customer journey.`;

            const apiKey = await window.electronAPI.getApiKey();
            if (!apiKey) {
                throw new Error('OpenAI API key not found. Please configure it in Settings.');
            }

            const response = await fetch('https://api.openai.com/v1/chat/completions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: 'gpt-4',
                    messages: [{
                        role: 'user',
                        content: storyPrompt
                    }],
                    max_tokens: 2000
                })
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.statusText}`);
            }

            const data = await response.json();
            const storyContent = data.choices[0].message.content;

            // Step 2: Process story into scenes
            this.updateModalProgress(50, 'Processing story scenes...');
            
            // Parse the story content into scenes
            const scenes = this.parseStoryIntoScenes(storyContent, advancedData.sceneCount);
            
            // Step 3: Generate image prompts
            this.updateModalProgress(70, 'Creating image prompts...');
            
            const enhancedScenes = scenes.map((scene, index) => ({
                ...scene,
                scene_id: index + 1,
                dalle_prompt: this.createImagePrompt(scene, imageData, brandData),
                image_path: `scene_${index + 1}.png`
            }));

            // Step 4: Complete
            this.updateModalProgress(100, 'Story preview ready!');
            await this.delay(1000);

            this.hideGenerationModal();
            
            // Store the generated story
            this.currentStory = {
                story_name: projectData.projectName,
                scenes: enhancedScenes,
                metadata: {
                    brand: brandData,
                    project: projectData,
                    image_style: imageData,
                    advanced: advancedData,
                    generated_at: new Date().toISOString()
                }
            };

            // Update preview
            this.updateStoryPreview();
            this.showStatus('✅ Story preview generated successfully!', 'success');

        } catch (error) {
            this.hideGenerationModal();
            this.showStatus(`❌ Error generating story: ${error.message}`, 'error');
            console.error('Story generation error:', error);
        }
    }

    parseStoryIntoScenes(storyContent, sceneCount) {
        // Simple parsing - in a real app, this would be more sophisticated
        const lines = storyContent.split('\n').filter(line => line.trim());
        const scenes = [];
        
        for (let i = 0; i < sceneCount; i++) {
            const sceneContent = lines.slice(i * 2, (i + 1) * 2).join(' ') || `Scene ${i + 1}: Customer interaction point`;
            scenes.push({
                persona: 'Customer',
                story_segment: sceneContent,
                scene_description: sceneContent
            });
        }
        
        return scenes;
    }

    createImagePrompt(scene, imageData, brandData) {
        let prompt = scene.story_segment;
        
        // Add style information
        if (imageData.imageStyle && imageData.imageStyle !== 'default') {
            prompt += `, ${imageData.imageStyle} style`;
        }
        
        // Add brand colors if specified
        if (brandData.primaryColor) {
            prompt += `, incorporating ${brandData.primaryColor} color scheme`;
        }
        
        // Add quality and format preferences
        prompt += ', high quality, professional, clean composition';
        
        return prompt;
    }

    updateStoryPreview() {
        if (!this.currentStory) return;

        const previewContainer = document.getElementById('storyPreview');
        if (!previewContainer) return;

        let previewHTML = `
            <div class="story-header">
                <h3>${this.currentStory.story_name}</h3>
                <p>Generated ${this.currentStory.scenes.length} scenes</p>
            </div>
            <div class="scenes-grid">
        `;

        this.currentStory.scenes.forEach(scene => {
            previewHTML += `
                <div class="scene-card">
                    <div class="scene-number">Scene ${scene.scene_id}</div>
                    <div class="scene-content">
                        <p><strong>Persona:</strong> ${scene.persona}</p>
                        <p><strong>Story:</strong> ${scene.story_segment}</p>
                        <p><strong>Image Prompt:</strong> ${scene.dalle_prompt}</p>
                    </div>
                </div>
            `;
        });

        previewHTML += '</div>';
        previewContainer.innerHTML = previewHTML;
        
        // Show download section
        document.getElementById('downloadSection').style.display = 'block';
    }

    async downloadStoryboard() {
        // Check if we have PowerPoint data to download
        if (this.currentPowerPointData) {
            console.log('Downloading PowerPoint data:', this.currentPowerPointData);
            return this.downloadGeneratedFile(this.currentPowerPointData);
        }

        // Fallback to JSON download if no PowerPoint data
        if (!this.currentStory) {
            this.showStatus('❌ No storyboard to download', 'error');
            return;
        }

        try {
            // Show generation modal for download process
            this.showGenerationModal();
            this.updateModalProgress(20, 'Preparing storyboard download...');

            // Create downloadable content
            const storyboardData = {
                ...this.currentStory,
                download_info: {
                    format: 'JSON',
                    created_at: new Date().toISOString(),
                    version: '1.0'
                }
            };

            this.updateModalProgress(80, 'Creating download file...');

            // Convert to JSON string
            const jsonContent = JSON.stringify(storyboardData, null, 2);

            // Create blob and download
            const blob = new Blob([jsonContent], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `${this.currentStory.story_name.replace(/[^a-z0-9]/gi, '_')}_storyboard.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            URL.revokeObjectURL(url);

            this.updateModalProgress(100, 'Download complete!');
            await this.delay(1000);

            this.hideGenerationModal();
            this.showStatus('✅ Storyboard downloaded successfully!', 'success');

        } catch (error) {
            this.hideGenerationModal();
            this.showStatus(`❌ Download failed: ${error.message}`, 'error');
            console.error('Download error:', error);
        }
    }

    // Modal management methods
    showGenerationModal() {
        const modal = document.getElementById('generationModal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    hideGenerationModal() {
        const modal = document.getElementById('generationModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    updateModalProgress(percentage, message) {
        const progressBar = document.getElementById('modalProgressFill');
        const progressText = document.getElementById('modalProgressText');

        if (progressBar && percentage !== null) {
            progressBar.style.width = `${percentage}%`;
        }

        if (progressText && message) {
            progressText.textContent = message;
        }
    }

    // Utility methods
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    resetAll() {
        if (confirm('Are you sure you want to reset all settings? This will clear all your current configuration.')) {
            // Reset form fields
            document.getElementById('description').value = '';
            document.getElementById('expectations').value = '';
            document.getElementById('nbSteps').value = '0';
            document.getElementById('baseStyle').value = 'SKETCHY_BW_GRAPHIC';
            document.getElementById('aspectRatio').value = 'WIDESCREEN';
            document.getElementById('outputLanguage').value = 'English';
            document.getElementById('includeEmotions').checked = true;
            document.getElementById('includeChallenges').checked = true;

            // Reset brand config
            this.brandConfig = {
                brand_name: '',
                logo_data: null,
                logo_position: 'top-right',
                logo_size: [100, 50],
                primary_color: [52, 152, 219],
                secondary_color: [46, 204, 113],
                accent_color: [241, 196, 15],
                background_color: [248, 249, 250],
                text_color: [44, 62, 80],
                primary_font: 'Arial',
                secondary_font: 'Arial'
            };

            // Reset color inputs
            document.getElementById('primaryColor').value = '#3498db';
            document.getElementById('secondaryColor').value = '#2ecc71';
            document.getElementById('accentColor').value = '#f1c40f';
            document.getElementById('logoPosition').value = 'top-right';

            // Clear logo preview
            document.getElementById('logoPreview').innerHTML = '';
            document.getElementById('logoUpload').value = '';

            // Reset avoided terms
            this.avoidedTerms = [];
            this.updateAvoidedTermsList();

            // Clear current story and download section
            this.currentStory = null;
            this.currentPowerPointData = null;

            const downloadSection = document.getElementById('downloadSection');
            if (downloadSection) downloadSection.style.display = 'none';

            const storyPreviewSection = document.getElementById('storyPreviewSection');
            if (storyPreviewSection) storyPreviewSection.style.display = 'none';

            // Update UI
            this.updateSettingsSummary();
            this.updateStyleDescription('SKETCHY_BW_GRAPHIC');
            this.validateForm();

            // Switch to first tab
            this.switchTab('project-setup');

            this.showStatus('✅ All settings have been reset', 'success');
        }
    }

    showStatus(message, type) {
        // Create or update status message
        let statusEl = document.getElementById('statusMessage');
        if (!statusEl) {
            statusEl = document.createElement('div');
            statusEl.id = 'statusMessage';
            statusEl.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 10px 20px;
                border-radius: 5px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                max-width: 300px;
            `;
            document.body.appendChild(statusEl);
        }

        statusEl.textContent = message;
        statusEl.className = type;
        
        // Style based on type
        if (type === 'success') {
            statusEl.style.backgroundColor = '#4CAF50';
        } else if (type === 'error') {
            statusEl.style.backgroundColor = '#f44336';
        } else {
            statusEl.style.backgroundColor = '#2196F3';
        }

        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (statusEl.parentNode) {
                statusEl.parentNode.removeChild(statusEl);
            }
        }, 5000);
    }

    resetForm() {
        // Reset all form fields
        const forms = document.querySelectorAll('form');
        forms.forEach(form => form.reset());
        
        // Clear preview and download sections
        const preview = document.getElementById('storyPreview');
        if (preview) preview.innerHTML = '';
        
        const downloadSection = document.getElementById('downloadSection');
        if (downloadSection) downloadSection.style.display = 'none';
        
        // Clear current story
        this.currentStory = null;
        
        this.showStatus('Form reset successfully', 'success');
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing StoryboardApp...');
    try {
        window.storyboardApp = new StoryboardApp();
        console.log('StoryboardApp initialized successfully');
    } catch (error) {
        console.error('Error initializing StoryboardApp:', error);
    }
});

// Global function for paste buttons
async function pasteFromClipboard(fieldId) {
    try {
        const text = await navigator.clipboard.readText();
        const field = document.getElementById(fieldId);
        if (field && text) {
            field.value = text;
            field.focus();
            
            // Auto-resize if it's a textarea
            if (field.tagName === 'TEXTAREA') {
                field.style.height = 'auto';
                field.style.height = Math.max(120, field.scrollHeight) + 'px';
            }
            
            // Update settings summary if app is initialized
            if (window.storyboardApp) {
                window.storyboardApp.updateSettingsSummary();
            }
            
            const fieldName = fieldId === 'description' ? 'Project Description' : 'Customer Insights';
            
            // Show success message
            if (window.storyboardApp) {
                window.storyboardApp.showStatus(`📋 Pasted content into ${fieldName}`, 'success');
            }
        }
    } catch (err) {
        console.error('Failed to read clipboard:', err);
        const errorMsg = '❌ Could not access clipboard. Please use Ctrl+V (Cmd+V on Mac)';
        
        if (window.storyboardApp) {
            window.storyboardApp.showStatus(errorMsg, 'error');
        } else {
            alert(errorMsg);
        }
    }
}

// Global functions for avoided terms
function addAvoidedTerm() {
    if (window.storyboardApp) {
        window.storyboardApp.addAvoidedTerm();
    }
}

function clearAvoidedTerms() {
    if (window.storyboardApp) {
        window.storyboardApp.clearAvoidedTerms();
    }
}

// Debug functions for API key testing
async function testApiKeyAccess() {
    const debugOutput = document.getElementById('debugOutput');
    debugOutput.style.display = 'block';
    
    try {
        debugOutput.innerHTML = 'Testing API key access...<br>';
        
        const apiKey = await window.electronAPI.getApiKey();
        debugOutput.innerHTML += `✅ API Key fetched: ${apiKey ? `${apiKey.substring(0, 15)}...` : 'null/empty'}<br>`;
        debugOutput.innerHTML += `📊 Key length: ${apiKey ? apiKey.length : 0} characters<br>`;
        debugOutput.innerHTML += `🔍 Starts with 'sk-': ${apiKey && apiKey.startsWith('sk-') ? 'Yes' : 'No'}<br>`;
        
        if (window.storyboardApp) {
            await window.storyboardApp.refreshApiKey();
            debugOutput.innerHTML += '🔄 App API key refreshed<br>';
        }
        
        debugOutput.innerHTML += '✅ Test completed successfully!';
        
    } catch (error) {
        debugOutput.innerHTML += `❌ Error: ${error.message}`;
        console.error('API key test error:', error);
    }
}

async function refreshApiKeyStatus() {
    if (window.storyboardApp) {
        const apiKey = await window.storyboardApp.refreshApiKey();
        const message = apiKey ? '✅ API key refreshed successfully' : '❌ No API key found';
        window.storyboardApp.showStatus(message, apiKey ? 'success' : 'error');
    }
}
